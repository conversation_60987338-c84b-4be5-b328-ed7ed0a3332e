<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="CargoProjects">
    <cargoProject FILE="$PROJECT_DIR$/Cargo.toml">
      <package file="$PROJECT_DIR$">
        <enabledFeature name="default" />
      </package>
    </cargoProject>
  </component>
  <component name="ChangeListManager">
    <list default="true" id="02e2cd2f-b621-4740-8a6d-f71b226be027" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="RsBuildProfile:dev" />
  <component name="MacroExpansionManager">
    <option name="directoryName" value="bs7Lj7i0" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2y5aPwvBKEDWIjFUCu8q1sK0nOy" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Cargo.Run ecgi_distance_calculator.executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Python.process_excel_data.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.rust.reset.selective.auto.import&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;org.rust.cargo.project.model.PROJECT_DISCOVERY&quot;: &quot;true&quot;,
    &quot;org.rust.cargo.project.model.impl.CargoExternalSystemProjectAware.subscribe.first.balloon&quot;: &quot;&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Cargo.Run ecgi_distance_calculator">
    <configuration name="Run ecgi_distance_calculator" type="CargoCommandRunConfiguration" factoryName="Cargo Command" temporary="true">
      <option name="buildProfileId" value="dev" />
      <option name="command" value="run --package ecgi_distance_calculator --bin ecgi_distance_calculator" />
      <option name="workingDirectory" value="file://$PROJECT_DIR$" />
      <envs />
      <option name="emulateTerminal" value="true" />
      <option name="channel" value="DEFAULT" />
      <option name="requiredFeatures" value="true" />
      <option name="allFeatures" value="false" />
      <option name="withSudo" value="false" />
      <option name="buildTarget" value="REMOTE" />
      <option name="backtrace" value="SHORT" />
      <option name="isRedirectInput" value="false" />
      <option name="redirectInputPath" value="" />
      <method v="2">
        <option name="CARGO.BUILD_TASK_PROVIDER" enabled="true" />
      </method>
    </configuration>
    <configuration name="process_excel_data" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="同频站点距离计算" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/process_excel_data.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Cargo.Run ecgi_distance_calculator" />
      </list>
    </recent_temporary>
  </component>
  <component name="RustProjectSettings">
    <option name="toolchainHomeDirectory" value="$USER_HOME$/.cargo/bin" />
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-PY-251.23774.444" />
        <option value="bundled-python-sdk-890ed5b35930-d9c5bdb153f4-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.23774.444" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="02e2cd2f-b621-4740-8a6d-f71b226be027" name="更改" comment="" />
      <created>1749126868250</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749126868250</updated>
      <workItem from="1749126870026" duration="628000" />
      <workItem from="1749127596378" duration="54000" />
      <workItem from="1749128508629" duration="677000" />
      <workItem from="1749171846709" duration="10627000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/$process_excel_data.coverage" NAME="process_excel_data 覆盖结果" MODIFIED="1749126880205" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/process_excel_data_py$process_excel_data.coverage" NAME="process_excel_data 覆盖结果" MODIFIED="1749179009622" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>