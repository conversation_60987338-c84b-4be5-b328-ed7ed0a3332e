use anyhow::{Context, Result};
use calamine::{open_workbook, Data, Reader, Xlsx};
use eframe::egui;
use egui::{Color32, RichText, Stroke};
use rfd::FileDialog;
use rust_xlsxwriter::{Color, Format, FormatAlign, FormatBorder, Workbook};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::f64::consts::PI;
use std::path::PathBuf;
use std::sync::{Arc, Mutex};
use std::thread;

/// 设置中文字体支持
fn setup_chinese_fonts(ctx: &egui::Context) {
    use std::sync::Once;
    static INIT: Once = Once::new();
    
    INIT.call_once(|| {
        let mut fonts = egui::FontDefinitions::default();
        
        // 尝试从系统加载中文字体
        let chinese_font_paths = vec![
            "C:/Windows/Fonts/simhei.ttf",     // 黑体
            "C:/Windows/Fonts/simsun.ttc",     // 宋体  
            "C:/Windows/Fonts/msyh.ttc",       // 微软雅黑
            "C:/Windows/Fonts/simkai.ttf",     // 楷体
            "C:/Windows/Fonts/simfang.ttf",    // 仿宋
        ];
        
        let mut font_loaded = false;
        
        for (i, font_path) in chinese_font_paths.iter().enumerate() {
            if let Ok(font_data) = std::fs::read(font_path) {
                let font_name = format!("ChineseFont{}", i);
                fonts.font_data.insert(
                    font_name.clone(),
                    egui::FontData::from_owned(font_data),
                );
                
                // 将中文字体添加到字体族
                fonts.families
                    .entry(egui::FontFamily::Proportional)
                    .or_default()
                    .insert(0, font_name.clone());
                    
                fonts.families
                    .entry(egui::FontFamily::Monospace)
                    .or_default()
                    .insert(0, font_name);
                    
                font_loaded = true;
                break;
            }
        }
        
        if !font_loaded {
            println!("警告：未找到中文字体文件，使用默认字体");
        }
        
        ctx.set_fonts(fonts);
    });
}

/// 计算模式
#[derive(Debug, Clone, PartialEq)]
enum CalculationMode {
    ByFrequency,      // 按频点计算
    DistanceOnly,     // 仅计算距离
    Bidirectional,    // 双向计算
}

/// 站点数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
struct SiteData {
    ecgi: String,
    site_number: String,
    longitude: f64,
    latitude: f64,
    frequency: i32,
}

/// 匹配结果结构
#[derive(Debug, Clone)]
struct MatchResult {
    source_ecgi: String,
    source_site_number: String,
    source_longitude: f64,
    source_latitude: f64,
    source_frequency: i32,
    target_ecgi: String,
    target_site_number: String,
    target_longitude: f64,
    target_latitude: f64,
    target_frequency: i32,
    distance_km: f64,
    frequency_match: bool,
}

/// 自定义错误类型
#[derive(thiserror::Error, Debug)]
enum ProcessingError {
    #[error("文件不存在: {filename}")]
    FileNotFound { filename: String },
    
    #[error("Sheet不存在: {sheet_name}")]
    SheetNotFound { sheet_name: String },
    
    #[error("缺失必需列: {columns:?}")]
    MissingColumns { columns: Vec<String> },
    
    #[error("数据格式错误: {message}")]
    DataFormatError { message: String },
}

/// 处理状态
#[derive(Debug, Clone)]
enum ProcessingStatus {
    Idle,
    Loading,
    Processing(f32), // 进度百分比
    Completed(String), // 完成消息
    Error(String),
}

/// 弹窗状态
#[derive(Debug, Clone)]
enum ModalState {
    None,
    Processing,
    Results(String),
    Error(String),
}

/// GUI应用结构
struct EcgiCalculatorApp {
    input_file_path: Option<PathBuf>,
    output_file_path: Option<PathBuf>,
    calculation_mode: CalculationMode,
    processing_status: Arc<Mutex<ProcessingStatus>>,
    results_stats: Option<String>,
    modal_state: ModalState,
    show_results_modal: bool,
}

impl Default for EcgiCalculatorApp {
    fn default() -> Self {
        Self {
            input_file_path: None,
            output_file_path: None,
            calculation_mode: CalculationMode::Bidirectional,
            processing_status: Arc::new(Mutex::new(ProcessingStatus::Idle)),
            results_stats: None,
            modal_state: ModalState::None,
            show_results_modal: false,
        }
    }
}

impl eframe::App for EcgiCalculatorApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        // 设置中文字体支持
        setup_chinese_fonts(ctx);
        
        // 检查处理状态并更新results_stats
        let status = self.processing_status.lock().unwrap().clone();
        if let ProcessingStatus::Completed(stats) = status {
            if self.results_stats.is_none() {
                self.results_stats = Some(stats);
            }
        }
        
        // 设置优化的深色主题 - 更丰富的色彩层次
        ctx.set_visuals(egui::Visuals {
            dark_mode: true,
            override_text_color: Some(Color32::from_rgb(230, 240, 235)),
            window_fill: Color32::from_rgb(18, 24, 22),  // 主背景色
            panel_fill: Color32::from_rgb(22, 28, 25),   // 面板背景
            faint_bg_color: Color32::from_rgb(28, 35, 31),
            extreme_bg_color: Color32::from_rgb(14, 18, 16),
            code_bg_color: Color32::from_rgb(25, 30, 27),
            warn_fg_color: Color32::from_rgb(255, 180, 60),
            error_fg_color: Color32::from_rgb(255, 120, 120),
            window_stroke: Stroke::new(1.5, Color32::from_rgb(60, 180, 120)),
            ..egui::Visuals::dark()
        });

        egui::CentralPanel::default().show(ctx, |ui| {
            // 设置合理的间距
            ui.style_mut().spacing.item_spacing = egui::vec2(8.0, 6.0);
            ui.style_mut().spacing.button_padding = egui::vec2(12.0, 6.0); 
            ui.style_mut().spacing.indent = 16.0;
            
            // 使用滚动区域确保内容可见
            egui::ScrollArea::vertical()
                .auto_shrink([false; 2])
                .show(ui, |ui| {
                    // 优化内容宽度，确保适应不同屏幕
                    let available_width = ui.available_width();
                    let optimal_width = (available_width * 0.9).min(1000.0).max(600.0);
                    
                    ui.allocate_ui_with_layout(
                        egui::vec2(optimal_width, ui.available_height()),
                        egui::Layout::top_down(egui::Align::Center),
                        |ui| {
                            ui.add_space(8.0);
                            
                            // 极简标题区域
                            self.draw_optimized_header(ui);
                            
                            ui.add_space(8.0);
                            
                            ui.vertical(|ui| {
                     
                                self.draw_compact_mode_card(ui);
                                
                                ui.add_space(8.0);
                                
                                // 第二行：文件操作卡片 - 优化布局
                                self.draw_optimized_file_card(ui, ctx);
                                
                                ui.add_space(8.0);
                                
                                // 第三行：状态和结果卡片 - 紧凑设计
                                self.draw_compact_status_card(ui);
                            });
                            
                            ui.add_space(12.0);
                        }
                    );
                });
            
            // 处理弹窗
            self.show_modals(ctx);
        });
    }
}

impl EcgiCalculatorApp {
    fn draw_optimized_header(&mut self, ui: &mut egui::Ui) {
        // 简洁的标题框 - 完全符合当前框架风格
        egui::Frame::none()
            .fill(Color32::from_rgb(25, 35, 30))
            .stroke(Stroke::new(1.5, Color32::from_rgb(70, 180, 120)))
            .rounding(egui::Rounding::same(8.0))
            .inner_margin(egui::Margin::symmetric(14.0, 8.0))
            .show(ui, |ui| {
                ui.horizontal(|ui| {
                    ui.label(RichText::new("🌐").size(14.0).color(Color32::from_rgb(80, 220, 160)));
                    ui.add_space(4.0);
                    ui.label(
                        RichText::new("ECGI站点距离计算器")
                            .size(13.0)
                            .color(Color32::from_rgb(80, 220, 160))
                            .strong()
                    );
                    ui.add_space(4.0);
                    ui.label(
                        RichText::new("v2.0")
                            .size(10.0)
                            .color(Color32::from_rgb(140, 180, 160))
                    );
                    ui.add_space(4.0);
                    ui.label(RichText::new("📡").size(14.0).color(Color32::from_rgb(80, 220, 160)));
                });
            });
    }

    fn draw_compact_mode_card(&mut self, ui: &mut egui::Ui) {
        // 超紧凑的模式选择卡片
        egui::Frame::none()
            .fill(Color32::from_rgb(25, 35, 30))
            .stroke(Stroke::new(1.5, Color32::from_rgb(70, 180, 120)))
            .rounding(egui::Rounding::same(8.0))
            .inner_margin(egui::Margin::symmetric(14.0, 8.0))  // 大幅减小内边距
            .show(ui, |ui| {
                ui.vertical(|ui| {
                    // 超紧凑的卡片标题
                    ui.horizontal(|ui| {
                        ui.label(RichText::new("⚡").size(14.0).color(Color32::from_rgb(255, 200, 80)));
                        ui.add_space(4.0);
                        ui.label(
                            RichText::new("计算模式选择")
                                .size(13.0)  // 更小的标题
                                .color(Color32::from_rgb(255, 220, 100))
                                .strong()
                        );
                    });
                    
                    ui.add_space(6.0);  // 减少间距
                    
                    // 模式选择按钮 - 更紧凑的水平布局
                    ui.horizontal(|ui| {
                        let button_width = (ui.available_width() - 16.0) / 3.0;
                        
                        // 按频点优先
                        let freq_selected = matches!(self.calculation_mode, CalculationMode::ByFrequency);
                        if ui.add(
                            egui::Button::new(
                                RichText::new("🎯\n按频点优先")
                                    .size(12.0)  // 稍小的字体
                                    .strong()
                            )
                            .min_size(egui::vec2(button_width, 36.0))  // 大幅减小高度
                            .fill(if freq_selected {
                                Color32::from_rgb(60, 180, 120)
                            } else {
                                Color32::from_rgb(40, 50, 45)
                            })
                        ).clicked() {
                            self.calculation_mode = CalculationMode::ByFrequency;
                        }
                        
                        ui.add_space(8.0);
                        
                        // 仅计算距离
                        let dist_selected = matches!(self.calculation_mode, CalculationMode::DistanceOnly);
                        if ui.add(
                            egui::Button::new(
                                RichText::new("📏\n仅按距离")
                                    .size(12.0)
                                    .strong()
                            )
                            .min_size(egui::vec2(button_width, 36.0))
                            .fill(if dist_selected {
                                Color32::from_rgb(60, 180, 120)
                            } else {
                                Color32::from_rgb(40, 50, 45)
                            })
                        ).clicked() {
                            self.calculation_mode = CalculationMode::DistanceOnly;
                        }
                        
                        ui.add_space(8.0);
                        
                        // 双向计算
                        let bi_selected = matches!(self.calculation_mode, CalculationMode::Bidirectional);
                        if ui.add(
                            egui::Button::new(
                                RichText::new("🔄\n双向计算")
                                    .size(12.0)
                                    .strong()
                            )
                            .min_size(egui::vec2(button_width, 36.0))
                            .fill(if bi_selected {
                                Color32::from_rgb(60, 180, 120)
                            } else {
                                Color32::from_rgb(40, 50, 45)
                            })
                        ).clicked() {
                            self.calculation_mode = CalculationMode::Bidirectional;
                        }
                    });
                    
                    ui.add_space(6.0);  // 减少间距
                    
                    // 简化的模式说明
                    let description = match self.calculation_mode {
                        CalculationMode::ByFrequency => "优先匹配相同频点的站点，提高频点匹配准确率",
                        CalculationMode::DistanceOnly => "忽略频点差异，仅按物理距离匹配最近站点", 
                        CalculationMode::Bidirectional => "同时计算原始→目标和目标→原始双向匹配结果",
                    };
                    
                    ui.horizontal_wrapped(|ui| {
                        ui.label(RichText::new("ℹ️").size(12.0));
                        ui.add_space(3.0);
                        ui.label(
                            RichText::new(description)
                                .size(10.0)  // 稍小的说明文字
                                .color(Color32::from_rgb(170, 200, 185))
                                .italics()
                        );
                    });
                });
            });
    }

    fn draw_optimized_file_card(&mut self, ui: &mut egui::Ui, ctx: &egui::Context) {
        // 优化的文件操作卡片
        egui::Frame::none()
            .fill(Color32::from_rgb(25, 35, 30))
            .stroke(Stroke::new(1.8, Color32::from_rgb(70, 180, 120)))
            .rounding(egui::Rounding::same(10.0))
            .inner_margin(egui::Margin::symmetric(18.0, 12.0))
            .show(ui, |ui| {
                ui.vertical(|ui| {
                    // 紧凑的卡片标题
                    ui.horizontal(|ui| {
                        ui.label(RichText::new("📁").size(16.0).color(Color32::from_rgb(100, 200, 255)));
                        ui.add_space(6.0);
                        ui.label(
                            RichText::new("文件选择与处理")
                                .size(15.0)
                                .color(Color32::from_rgb(120, 220, 255))
                                .strong()
                        );
                    });
                    
                    ui.add_space(8.0);
                    
                    // 更紧凑的文件选择区域
                    ui.horizontal(|ui| {
                        let half_width = (ui.available_width() - 16.0) / 2.0;
                        
                        // 输入文件
                        ui.allocate_ui_with_layout(
                            egui::vec2(half_width, 80.0),
                            egui::Layout::top_down(egui::Align::LEFT),
                            |ui| {
                                ui.label(
                                    RichText::new("📥 输入文件")
                                        .size(13.0)
                                        .color(Color32::from_rgb(120, 200, 255))
                                        .strong()
                                );
                                ui.add_space(3.0);
                                
                                if let Some(path) = &self.input_file_path {
                                    ui.label(
                                        RichText::new(format!("✅ {}", 
                                            path.file_name()
                                                .unwrap_or_default()
                                                .to_string_lossy()
                                        ))
                                        .size(10.0)  // 更小的字体
                                        .color(Color32::from_rgb(100, 255, 150))
                                    );
                                } else {
                                    ui.label(
                                        RichText::new("⏳ 未选择文件")
                                            .size(10.0)
                                            .color(Color32::from_rgb(255, 180, 120))
                                    );
                                }
                                
                                if ui.add(
                                    egui::Button::new("📂 选择输入文件")
                                        .min_size(egui::vec2(half_width - 10.0, 28.0))  // 更小的按钮
                                ).clicked() {
                                    if let Some(path) = FileDialog::new()
                                        .add_filter("Excel文件", &["xlsx", "xls"])
                                        .set_title("选择包含站点数据的Excel文件")
                                        .pick_file() 
                                    {
                                        self.input_file_path = Some(path);
                                    }
                                }
                            }
                        );
                            
                            ui.add_space(16.0);
                            
                        // 输出文件
                        ui.allocate_ui_with_layout(
                            egui::vec2(half_width, 80.0),
                            egui::Layout::top_down(egui::Align::LEFT),
                            |ui| {
                                ui.label(
                                    RichText::new("📤 输出文件")
                                        .size(13.0)
                                        .color(Color32::from_rgb(120, 255, 180))
                                        .strong()
                                );
                                ui.add_space(3.0);
                                
                                if let Some(path) = &self.output_file_path {
                                    ui.label(
                                        RichText::new(format!("✅ {}", 
                                            path.file_name()
                                                .unwrap_or_default()
                                                .to_string_lossy()
                                        ))
                                        .size(10.0)
                                        .color(Color32::from_rgb(100, 255, 150))
                                    );
                                } else {
                                    ui.label(
                                        RichText::new("⏳ 未设置输出路径")
                                            .size(10.0)
                                            .color(Color32::from_rgb(255, 180, 120))
                                    );
                                }
                                
                                if ui.add(
                                    egui::Button::new("💾 设置输出路径")
                                        .min_size(egui::vec2(half_width - 10.0, 28.0))
                                ).clicked() {
                                    if let Some(path) = FileDialog::new()
                                        .add_filter("Excel文件", &["xlsx"])
                                        .set_title("选择结果文件保存位置")
                                        .save_file() 
                                    {
                                        self.output_file_path = Some(path);
                                    }
                                }
                            }
                        );
                    });
                    
                    ui.add_space(10.0);
                    
                    // 紧凑的计算按钮区域
                    ui.separator();
                    ui.add_space(8.0);
                    
                    let can_process = self.input_file_path.is_some() && 
                                      self.output_file_path.is_some() &&
                                      matches!(*self.processing_status.lock().unwrap(), ProcessingStatus::Idle | ProcessingStatus::Completed(_) | ProcessingStatus::Error(_));
                    
                    ui.horizontal_centered(|ui| {
                        if ui.add_enabled(
                            can_process,
                            egui::Button::new(
                                RichText::new(if can_process {
                                    "🚀 开始智能计算"
                                } else {
                                    "⚠️ 请完成文件选择"
                                })
                                .size(14.0)  // 稍小的字体
                                .strong()
                            )
                            .min_size(egui::vec2(180.0, 40.0))  // 稍小的按钮
                            .fill(if can_process {
                                Color32::from_rgb(40, 150, 80)
                            } else {
                                Color32::from_rgb(120, 70, 50)
                            })
                        ).clicked() {
                            self.start_processing(ctx);
                        }
                    });
                    
                    ui.add_space(8.0);
                });
            });
    }

    fn draw_compact_status_card(&mut self, ui: &mut egui::Ui) {
        // 紧凑的状态和结果卡片
        egui::Frame::none()
            .fill(Color32::from_rgb(25, 35, 30))
            .stroke(Stroke::new(1.8, Color32::from_rgb(70, 180, 120)))
            .rounding(egui::Rounding::same(10.0))
            .inner_margin(egui::Margin::symmetric(18.0, 12.0))
            .show(ui, |ui| {
                ui.vertical(|ui| {
                    // 紧凑的卡片标题
                    ui.horizontal(|ui| {
                        ui.label(RichText::new("📊").size(16.0).color(Color32::from_rgb(255, 180, 100)));
                        ui.add_space(6.0);
                        ui.label(
                            RichText::new("处理状态与结果")
                                .size(15.0)
                                .color(Color32::from_rgb(255, 200, 120))
                                .strong()
                        );
                    });
                    
                    ui.add_space(8.0);
                    
                    let status = self.processing_status.lock().unwrap().clone();
                    match status {
                        ProcessingStatus::Idle => {
                            ui.vertical_centered(|ui| {
                                ui.add_space(10.0);
                                ui.label(RichText::new("⏳").size(40.0));  // 稍小的图标
                                ui.add_space(6.0);
                                ui.label(
                                    RichText::new("等待开始计算")
                                        .size(16.0)  // 稍小的字体
                                        .color(Color32::from_rgb(200, 220, 210))
                                        .strong()
                                );
                                ui.add_space(4.0);
                                ui.label(
                                    RichText::new("请完成文件选择并点击开始计算按钮")
                                        .size(11.0)  // 稍小的字体
                                        .color(Color32::from_rgb(150, 170, 160))
                                );
                                ui.add_space(10.0);
                            });
                        }
                        ProcessingStatus::Completed(_) => {
                            ui.vertical_centered(|ui| {
                                ui.add_space(8.0);
                                ui.label(RichText::new("✅").size(40.0));
                                ui.add_space(6.0);
                                ui.label(
                                    RichText::new("计算完成！")
                                        .size(16.0)
                                        .color(Color32::from_rgb(120, 255, 150))
                                        .strong()
                                );
                                ui.add_space(6.0);
                                
                                ui.horizontal_centered(|ui| {
                                    if ui.add(
                                        egui::Button::new("📊 查看详细结果")
                                            .min_size(egui::vec2(120.0, 28.0))  // 更小的按钮
                                    ).clicked() {
                                        self.show_results_modal = true;
                                    }
                                    
                                    ui.add_space(6.0);
                                    
                                    if ui.add(
                                        egui::Button::new("📁 打开文件夹")
                                            .min_size(egui::vec2(120.0, 28.0))
                                    ).clicked() {
                                        if let Some(output_path) = &self.output_file_path {
                                            if let Some(parent) = output_path.parent() {
                                                let _ = std::process::Command::new("explorer")
                                                    .arg(parent)
                                                    .spawn();
                                            }
                                        }
                                    }
                                });
                                
                                ui.add_space(8.0);
                            });
                        }
                        ProcessingStatus::Error(_) => {
                            ui.vertical_centered(|ui| {
                                ui.add_space(8.0);
                                ui.label(RichText::new("❌").size(40.0));
                                ui.add_space(6.0);
                                ui.label(
                                    RichText::new("处理失败")
                                        .size(16.0)
                                        .color(Color32::from_rgb(255, 120, 120))
                                        .strong()
                                );
                                ui.add_space(6.0);
                                
                                if ui.add(
                                    egui::Button::new("🔍 查看错误详情")
                                        .min_size(egui::vec2(140.0, 28.0))
                                ).clicked() {
                                    if let ProcessingStatus::Error(error_msg) = status {
                                        self.modal_state = ModalState::Error(error_msg);
                                    }
                                }
                                
                                ui.add_space(8.0);
                            });
                        }
                        _ => {
                            ui.vertical_centered(|ui| {
                                ui.add_space(10.0);
                                ui.spinner();
                                ui.add_space(6.0);
                                ui.label(
                                    RichText::new("🔄 正在处理中...")
                                        .size(14.0)  // 稍小的字体
                                        .color(Color32::from_rgb(120, 200, 255))
                                        .strong()
                                );
                                ui.add_space(10.0);
                            });
                        }
                    }
                });
            });
    }

    fn draw_new_header(&mut self, ui: &mut egui::Ui) {
        // 全新设计的简洁标题栏
        egui::Frame::none()
            .fill(Color32::from_rgb(28, 42, 35))
            .stroke(Stroke::new(2.0, Color32::from_rgb(80, 200, 140)))
            .rounding(egui::Rounding::same(12.0))
            .inner_margin(egui::Margin::symmetric(24.0, 16.0))
            .show(ui, |ui| {
                ui.horizontal_centered(|ui| {
                    // 应用图标
                    ui.label(RichText::new("🌐").size(36.0));
                            
                            ui.add_space(16.0);
                            
                    // 标题文本区域
                    ui.vertical_centered(|ui| {
                        ui.label(
                            RichText::new("ECGI站点距离计算器")
                                .size(28.0)
                                .color(Color32::from_rgb(80, 220, 160))
                                .strong()
                        );
                        ui.add_space(4.0);
                        ui.label(
                            RichText::new("智能同频站点距离匹配系统 v2.0")
                                .size(13.0)
                                .color(Color32::from_rgb(140, 180, 160))
                        );
                    });
                    
                    ui.add_space(16.0);
                    
                    // 应用图标
                    ui.label(RichText::new("📡").size(36.0));
                    });
                });
    }

    fn draw_mode_card(&mut self, ui: &mut egui::Ui) {
        // 计算模式选择卡片
        egui::Frame::none()
            .fill(Color32::from_rgb(25, 35, 30))
            .stroke(Stroke::new(2.0, Color32::from_rgb(70, 180, 120)))
            .rounding(egui::Rounding::same(12.0))
            .inner_margin(egui::Margin::symmetric(20.0, 16.0))
            .show(ui, |ui| {
                ui.vertical(|ui| {
                    // 卡片标题
                    ui.horizontal(|ui| {
                        ui.label(RichText::new("⚡").size(20.0).color(Color32::from_rgb(255, 200, 80)));
                        ui.add_space(8.0);
                        ui.label(
                            RichText::new("计算模式选择")
                                .size(18.0)
                                .color(Color32::from_rgb(255, 220, 100))
                                .strong()
                        );
                    });
                    
                    ui.add_space(12.0);
                    
                    // 模式选择按钮 - 水平布局
                    ui.horizontal(|ui| {
                        let button_width = (ui.available_width() - 16.0) / 3.0;
                        
                        // 按频点优先
                        let freq_selected = matches!(self.calculation_mode, CalculationMode::ByFrequency);
                        if ui.add(
                            egui::Button::new(
                                RichText::new("🎯\n按频点优先")
                                    .size(14.0)
                                    .strong()
                            )
                            .min_size(egui::vec2(button_width, 60.0))
                            .fill(if freq_selected {
                                Color32::from_rgb(60, 180, 120)
                            } else {
                                Color32::from_rgb(40, 50, 45)
                            })
                        ).clicked() {
                            self.calculation_mode = CalculationMode::ByFrequency;
                        }
                        
                        ui.add_space(8.0);
                        
                        // 仅计算距离
                        let dist_selected = matches!(self.calculation_mode, CalculationMode::DistanceOnly);
                        if ui.add(
                            egui::Button::new(
                                RichText::new("📏\n仅按距离")
                                    .size(14.0)
                                    .strong()
                            )
                            .min_size(egui::vec2(button_width, 60.0))
                            .fill(if dist_selected {
                                Color32::from_rgb(60, 180, 120)
                            } else {
                                Color32::from_rgb(40, 50, 45)
                            })
                        ).clicked() {
                            self.calculation_mode = CalculationMode::DistanceOnly;
                        }
                        
                        ui.add_space(8.0);
                        
                        // 双向计算
                        let bi_selected = matches!(self.calculation_mode, CalculationMode::Bidirectional);
                        if ui.add(
                            egui::Button::new(
                                RichText::new("🔄\n双向计算")
                                    .size(14.0)
                                    .strong()
                            )
                            .min_size(egui::vec2(button_width, 60.0))
                            .fill(if bi_selected {
                                Color32::from_rgb(60, 180, 120)
                            } else {
                                Color32::from_rgb(40, 50, 45)
                            })
                        ).clicked() {
                            self.calculation_mode = CalculationMode::Bidirectional;
                        }
                    });
                    
                    ui.add_space(8.0);
                    
                    // 模式说明
                    let description = match self.calculation_mode {
                        CalculationMode::ByFrequency => "优先匹配相同频点的站点，提高频点匹配准确率",
                        CalculationMode::DistanceOnly => "忽略频点差异，仅按物理距离匹配最近站点", 
                        CalculationMode::Bidirectional => "同时计算原始→目标和目标→原始双向匹配结果",
                    };
                    
                    ui.horizontal_wrapped(|ui| {
                        ui.label(RichText::new("ℹ️").size(14.0));
                        ui.add_space(4.0);
                        ui.label(
                            RichText::new(description)
                                .size(12.0)
                                .color(Color32::from_rgb(170, 200, 185))
                                .italics()
                        );
                    });
                });
            });
    }

    fn draw_file_card(&mut self, ui: &mut egui::Ui, ctx: &egui::Context) {
        // 文件操作卡片
        egui::Frame::none()
            .fill(Color32::from_rgb(25, 35, 30))
            .stroke(Stroke::new(2.0, Color32::from_rgb(70, 180, 120)))
            .rounding(egui::Rounding::same(12.0))
            .inner_margin(egui::Margin::symmetric(20.0, 16.0))
            .show(ui, |ui| {
                ui.vertical(|ui| {
                    // 卡片标题
                    ui.horizontal(|ui| {
                        ui.label(RichText::new("📁").size(20.0).color(Color32::from_rgb(100, 200, 255)));
                        ui.add_space(8.0);
                        ui.label(
                            RichText::new("文件选择与处理")
                                .size(18.0)
                                .color(Color32::from_rgb(120, 220, 255))
                                .strong()
                        );
                    });
                    
                    ui.add_space(12.0);
                    
                    // 文件选择区域
                    ui.horizontal(|ui| {
                        // 输入文件
                        ui.vertical(|ui| {
                            ui.label(
                                RichText::new("📥 输入文件")
                                    .size(14.0)
                                    .color(Color32::from_rgb(120, 200, 255))
                                    .strong()
                            );
                            ui.add_space(4.0);
                            
                            if let Some(path) = &self.input_file_path {
                                ui.label(
                                    RichText::new(format!("✅ {}", 
                                        path.file_name()
                                            .unwrap_or_default()
                                            .to_string_lossy()
                                    ))
                                    .size(12.0)
                                    .color(Color32::from_rgb(100, 255, 150))
                                );
                            } else {
                                ui.label(
                                    RichText::new("⏳ 未选择文件")
                                        .size(12.0)
                                        .color(Color32::from_rgb(255, 180, 120))
                                );
                            }
                            
                            if ui.add(
                                egui::Button::new("📂 选择输入文件")
                                    .min_size(egui::vec2(160.0, 32.0))
                            ).clicked() {
                                if let Some(path) = FileDialog::new()
                                    .add_filter("Excel文件", &["xlsx", "xls"])
                                    .set_title("选择包含站点数据的Excel文件")
                                    .pick_file() 
                                {
                                    self.input_file_path = Some(path);
                                }
                            }
                        });
                        
                        ui.add_space(20.0);
                        
                        // 输出文件
                        ui.vertical(|ui| {
                            ui.label(
                                RichText::new("📤 输出文件")
                                    .size(14.0)
                                    .color(Color32::from_rgb(120, 255, 180))
                                    .strong()
                            );
                            ui.add_space(4.0);
                            
                            if let Some(path) = &self.output_file_path {
                                ui.label(
                                    RichText::new(format!("✅ {}", 
                                        path.file_name()
                                            .unwrap_or_default()
                                            .to_string_lossy()
                                    ))
                                    .size(12.0)
                                    .color(Color32::from_rgb(100, 255, 150))
                                );
                            } else {
                                ui.label(
                                    RichText::new("⏳ 未设置输出路径")
                                        .size(12.0)
                                        .color(Color32::from_rgb(255, 180, 120))
                                );
                            }
                            
                            if ui.add(
                                egui::Button::new("💾 设置输出路径")
                                    .min_size(egui::vec2(160.0, 32.0))
                            ).clicked() {
                                if let Some(path) = FileDialog::new()
                                    .add_filter("Excel文件", &["xlsx"])
                                    .set_title("选择结果文件保存位置")
                                    .save_file() 
                                {
                                    self.output_file_path = Some(path);
                                }
                            }
                        });
                    });
                    
                    ui.add_space(16.0);
                    
                    // 计算按钮区域
                    ui.separator();
                    ui.add_space(12.0);
                    
                    let can_process = self.input_file_path.is_some() && 
                                      self.output_file_path.is_some() &&
                                      matches!(*self.processing_status.lock().unwrap(), ProcessingStatus::Idle | ProcessingStatus::Completed(_) | ProcessingStatus::Error(_));
                    
                    ui.horizontal_centered(|ui| {
                        if ui.add_enabled(
                            can_process,
                            egui::Button::new(
                                RichText::new(if can_process {
                                    "🚀 开始智能计算"
                                } else {
                                    "⚠️ 请完成文件选择"
                                })
                                .size(16.0)
                                .strong()
                            )
                            .min_size(egui::vec2(200.0, 48.0))
                            .fill(if can_process {
                                Color32::from_rgb(40, 150, 80)
                            } else {
                                Color32::from_rgb(120, 70, 50)
                            })
                        ).clicked() {
                            self.start_processing(ctx);
                        }
                    });
                });
            });
    }

    fn draw_status_card(&mut self, ui: &mut egui::Ui) {
        // 状态和结果卡片
        egui::Frame::none()
            .fill(Color32::from_rgb(25, 35, 30))
            .stroke(Stroke::new(2.0, Color32::from_rgb(70, 180, 120)))
            .rounding(egui::Rounding::same(12.0))
            .inner_margin(egui::Margin::symmetric(20.0, 16.0))
            .show(ui, |ui| {
                ui.vertical(|ui| {
                    // 卡片标题
                    ui.horizontal(|ui| {
                        ui.label(RichText::new("📊").size(20.0).color(Color32::from_rgb(255, 180, 100)));
                        ui.add_space(8.0);
                        ui.label(
                            RichText::new("处理状态与结果")
                                .size(18.0)
                                .color(Color32::from_rgb(255, 200, 120))
                                .strong()
                        );
                    });
                    
                    ui.add_space(12.0);
                    
                    let status = self.processing_status.lock().unwrap().clone();
                    match status {
                        ProcessingStatus::Idle => {
                            ui.vertical_centered(|ui| {
                                ui.add_space(15.0);
                                ui.label(RichText::new("⏳").size(48.0));
                                ui.add_space(8.0);
                                ui.label(
                                    RichText::new("等待开始计算")
                                        .size(18.0)
                                        .color(Color32::from_rgb(200, 220, 210))
                                        .strong()
                                );
                                ui.add_space(6.0);
                                ui.label(
                                    RichText::new("请完成文件选择并点击开始计算按钮")
                                        .size(13.0)
                                        .color(Color32::from_rgb(150, 170, 160))
                                );
                                ui.add_space(15.0);
                            });
                        }
                        ProcessingStatus::Completed(_) => {
                            ui.vertical_centered(|ui| {
                                ui.add_space(10.0);
                                ui.label(RichText::new("✅").size(48.0));
                                ui.add_space(8.0);
                                ui.label(
                                    RichText::new("计算完成！")
                                        .size(18.0)
                                        .color(Color32::from_rgb(120, 255, 150))
                                        .strong()
                                );
                                ui.add_space(8.0);
                                
                                ui.horizontal_centered(|ui| {
                                    if ui.add(
                                        egui::Button::new("📊 查看详细结果")
                                            .min_size(egui::vec2(130.0, 32.0))
                                    ).clicked() {
                                        self.show_results_modal = true;
                                    }
                                    
                                    ui.add_space(8.0);
                                    
                                    if ui.add(
                                        egui::Button::new("📁 打开文件夹")
                                            .min_size(egui::vec2(130.0, 32.0))
                                    ).clicked() {
                                        if let Some(output_path) = &self.output_file_path {
                                            if let Some(parent) = output_path.parent() {
                                                let _ = std::process::Command::new("explorer")
                                                    .arg(parent)
                                                    .spawn();
                                            }
                                        }
                                    }
                                });
                                
                                ui.add_space(10.0);
                            });
                        }
                        ProcessingStatus::Error(_) => {
                            ui.vertical_centered(|ui| {
                                ui.add_space(10.0);
                                ui.label(RichText::new("❌").size(48.0));
                                ui.add_space(8.0);
                                ui.label(
                                    RichText::new("处理失败")
                                        .size(18.0)
                                        .color(Color32::from_rgb(255, 120, 120))
                                        .strong()
                                );
                                ui.add_space(8.0);
                                
                                if ui.add(
                                    egui::Button::new("🔍 查看错误详情")
                                        .min_size(egui::vec2(150.0, 32.0))
                                ).clicked() {
                                    if let ProcessingStatus::Error(error_msg) = status {
                                        self.modal_state = ModalState::Error(error_msg);
                                    }
                                }
                                
                                ui.add_space(10.0);
                            });
                        }
                        _ => {
                            ui.vertical_centered(|ui| {
                                ui.add_space(15.0);
                                ui.spinner();
                                ui.add_space(8.0);
                                ui.label(
                                    RichText::new("🔄 正在处理中...")
                                        .size(16.0)
                                        .color(Color32::from_rgb(120, 200, 255))
                                        .strong()
                                );
                                ui.add_space(15.0);
                            });
                        }
                    }
                });
            });
    }

    fn draw_header_compact(&mut self, ui: &mut egui::Ui) {
        // 紧凑版标题栏设计 - 减小高度
        ui.vertical_centered(|ui| {
            ui.set_max_width(720.0);
            
            egui::Frame::none()
                .fill(Color32::from_rgb(25, 35, 30))
                .stroke(Stroke::new(2.0, Color32::from_rgb(80, 200, 140)))
                .rounding(egui::Rounding::same(10.0))
                .inner_margin(egui::Margin::symmetric(20.0, 12.0))  // 减小垂直内边距
                .show(ui, |ui| {
                    ui.horizontal_centered(|ui| {
                        // 左侧图标
                        ui.label(RichText::new("🌐").size(28.0));  // 减小图标尺寸
                        
                        ui.add_space(12.0);
                        
                        // 标题文字
                        ui.vertical(|ui| {
                            ui.label(
                                RichText::new("ECGI站点距离计算器")
                                    .size(24.0)  // 减小字体尺寸
                                    .color(Color32::from_rgb(80, 220, 160))
                                    .strong()
                            );
                            ui.add_space(2.0);
                            ui.label(
                                RichText::new("智能同频站点距离匹配系统 v2.0")
                                    .size(11.0)  // 减小字体尺寸
                                    .color(Color32::from_rgb(140, 180, 160))
                            );
                        });
                        
                        ui.add_space(12.0);
                        
                        // 右侧图标
                        ui.label(RichText::new("📡").size(28.0));  // 减小图标尺寸
                    });
                });
        });
    }

    fn draw_header(&mut self, ui: &mut egui::Ui) {
        // 优化的标题栏设计 - 固定宽度确保对齐
        ui.vertical_centered(|ui| {
            ui.set_max_width(720.0);
            
        egui::Frame::none()
                .fill(Color32::from_rgb(25, 35, 30))
                .stroke(Stroke::new(2.5, Color32::from_rgb(80, 200, 140)))
                .rounding(egui::Rounding::same(12.0))
                .inner_margin(egui::Margin::symmetric(30.0, 20.0))
            .show(ui, |ui| {
                    ui.vertical_centered(|ui| {
                        // 主标题区域
                ui.horizontal(|ui| {
                            ui.add_space(20.0);
                    
                            // 左侧图标
                            ui.label(RichText::new("🌐").size(40.0));
                    
                            ui.add_space(20.0);
                            
                            // 标题文字
                    ui.vertical(|ui| {
                        ui.label(
                                    RichText::new("ECGI站点距离计算器")
                                        .size(32.0)
                                        .color(Color32::from_rgb(80, 220, 160))
                                .strong()
                        );
                                ui.add_space(4.0);
                                ui.label(
                                    RichText::new("智能同频站点距离匹配系统 v2.0")
                                        .size(14.0)
                                        .color(Color32::from_rgb(140, 180, 160))
                                );
                            });
                            
                            ui.add_space(20.0);
                            
                            // 右侧图标
                            ui.label(RichText::new("📡").size(40.0));
                            
                            ui.add_space(20.0);
                    });
                });
            });
        });
    }
    
    fn draw_mode_selection_compact(&mut self, ui: &mut egui::Ui) {
        // 紧凑版功能模式选择区域
        egui::Frame::none()
            .fill(Color32::from_rgb(22, 30, 26))
            .stroke(Stroke::new(2.0, Color32::from_rgb(70, 180, 120)))
            .rounding(egui::Rounding::same(10.0))
            .inner_margin(egui::Margin::symmetric(16.0, 12.0))  // 减小内边距
            .show(ui, |ui| {
                ui.vertical(|ui| {
                    // 区域标题
                    ui.horizontal(|ui| {
                        ui.label(RichText::new("⚡").size(16.0).color(Color32::from_rgb(255, 200, 80)));
                        ui.label(
                            RichText::new("计算模式")
                                .size(14.0)  // 减小字体
                                .color(Color32::from_rgb(255, 220, 100))
                                .strong()
                        );
                    });
                    
                    ui.add_space(8.0);
                    
                    // 模式选择按钮 - 更紧凑的布局
                    self.draw_mode_buttons_compact(ui);
            });
        });
    }
    
    fn draw_mode_selection_area(&mut self, ui: &mut egui::Ui) {
        // 功能模式选择区域 - 统一框架设计
        egui::Frame::none()
            .fill(Color32::from_rgb(22, 30, 26))
            .stroke(Stroke::new(2.0, Color32::from_rgb(70, 180, 120)))
            .rounding(egui::Rounding::same(10.0))
            .inner_margin(egui::Margin::symmetric(24.0, 16.0))
            .show(ui, |ui| {
                ui.vertical(|ui| {
                    // 区域标题
                    ui.horizontal(|ui| {
                        ui.label(RichText::new("⚡").size(18.0).color(Color32::from_rgb(255, 200, 80)));
                        ui.label(
                            RichText::new("计算模式选择")
                                .size(16.0)
                                .color(Color32::from_rgb(255, 220, 100))
                                .strong()
                        );
                    });
                    
                    ui.add_space(12.0);
                    
                    // 模式选择按钮 - 改为垂直布局，更清晰
                    self.draw_mode_buttons(ui);
                });
            });
    }
    

    
    fn draw_file_operations_area(&mut self, ui: &mut egui::Ui, ctx: &egui::Context) {
        // 文件操作区域 - 大幅压缩高度，确保对齐
        egui::Frame::none()
            .fill(Color32::from_rgb(22, 30, 26))
            .stroke(Stroke::new(2.0, Color32::from_rgb(70, 180, 120)))
            .rounding(egui::Rounding::same(10.0))
            .inner_margin(egui::Margin::symmetric(16.0, 8.0))  // 进一步减小内边距
            .show(ui, |ui| {
                ui.vertical(|ui| {
                    // 区域标题 - 更紧凑
                    ui.horizontal(|ui| {
                        ui.label(RichText::new("📁").size(14.0).color(Color32::from_rgb(100, 200, 255)));
                        ui.label(
                            RichText::new("文件选择与处理")
                                .size(12.0)  // 减小字体
                                .color(Color32::from_rgb(120, 220, 255))
                                .strong()
                        );
                    });
                    
                    ui.add_space(6.0);  // 进一步减少间距
                    
                    // 文件选择区域 - 极度压缩布局
                    self.draw_file_selection_compact(ui);
                    
                    ui.add_space(6.0);  // 进一步减少间距
                    
                    // 处理控制区域
                    self.draw_processing_controls(ui, ctx);
                });
            });
    }

    fn draw_mode_buttons_compact(&mut self, ui: &mut egui::Ui) {
        // 紧凑版模式选择按钮
        ui.vertical(|ui| {
            let button_width = ui.available_width();
            let button_height = 28.0;  // 减小按钮高度
            
            // 按频点优先模式
            let freq_selected = matches!(self.calculation_mode, CalculationMode::ByFrequency);
            let freq_color = if freq_selected {
                Color32::from_rgb(60, 180, 120)
            } else {
                Color32::from_rgb(35, 45, 40)
            };
            
            if ui.add(
                egui::Button::new(RichText::new("🎯 按频点优先").size(12.0).strong())
                    .min_size(egui::vec2(button_width, button_height))
                    .fill(freq_color)
                    .stroke(if freq_selected {
                        Stroke::new(2.0, Color32::from_rgb(80, 220, 160))
                    } else {
                        Stroke::new(1.0, Color32::from_rgb(60, 80, 70))
                    })
            ).clicked() {
                self.calculation_mode = CalculationMode::ByFrequency;
            }
            
            ui.add_space(3.0);
            
            // 仅计算距离模式
            let dist_selected = matches!(self.calculation_mode, CalculationMode::DistanceOnly);
            let dist_color = if dist_selected {
                Color32::from_rgb(60, 180, 120)
            } else {
                Color32::from_rgb(35, 45, 40)
            };
            
            if ui.add(
                egui::Button::new(RichText::new("📏 仅按距离").size(12.0).strong())
                    .min_size(egui::vec2(button_width, button_height))
                    .fill(dist_color)
                    .stroke(if dist_selected {
                        Stroke::new(2.0, Color32::from_rgb(80, 220, 160))
                    } else {
                        Stroke::new(1.0, Color32::from_rgb(60, 80, 70))
                    })
            ).clicked() {
                self.calculation_mode = CalculationMode::DistanceOnly;
            }
            
            ui.add_space(3.0);
            
            // 双向计算模式
            let bi_selected = matches!(self.calculation_mode, CalculationMode::Bidirectional);
            let bi_color = if bi_selected {
                Color32::from_rgb(60, 180, 120)
            } else {
                Color32::from_rgb(35, 45, 40)
            };
            
            if ui.add(
                egui::Button::new(RichText::new("🔄 双向计算").size(12.0).strong())
                    .min_size(egui::vec2(button_width, button_height))
                    .fill(bi_color)
                    .stroke(if bi_selected {
                        Stroke::new(2.0, Color32::from_rgb(80, 220, 160))
                    } else {
                        Stroke::new(1.0, Color32::from_rgb(60, 80, 70))
                    })
            ).clicked() {
                self.calculation_mode = CalculationMode::Bidirectional;
            }
            
            ui.add_space(6.0);
            
            // 当前模式说明 - 简化显示
            let description = match self.calculation_mode {
                CalculationMode::ByFrequency => "优先匹配同频点站点",
                CalculationMode::DistanceOnly => "仅按距离匹配最近站点", 
                CalculationMode::Bidirectional => "双向匹配结果对比",
            };
            
            ui.horizontal_wrapped(|ui| {
                ui.label(RichText::new("ℹ️").size(10.0).color(Color32::from_rgb(150, 200, 180)));
                ui.label(
                    RichText::new(description)
                        .size(9.0)  // 减小字体
                        .color(Color32::from_rgb(150, 200, 180))
                        .italics()
                );
                });
            });
    }

    fn draw_mode_buttons(&mut self, ui: &mut egui::Ui) {
        // 改为垂直排列的模式选择按钮，更清晰易读
        ui.vertical(|ui| {
            let button_width = ui.available_width();
            let button_height = 36.0;  // 进一步减小按钮高度
            
            // 按频点优先模式
            let freq_selected = matches!(self.calculation_mode, CalculationMode::ByFrequency);
            let freq_color = if freq_selected {
                Color32::from_rgb(60, 180, 120)
                    } else {
                Color32::from_rgb(35, 45, 40)
            };
            
            if ui.add(
                egui::Button::new(RichText::new("🎯 按频点优先匹配").size(14.0).strong())
                    .min_size(egui::vec2(button_width, button_height))
                    .fill(freq_color)
                    .stroke(if freq_selected {
                        Stroke::new(2.0, Color32::from_rgb(80, 220, 160))
                    } else {
                        Stroke::new(1.0, Color32::from_rgb(60, 80, 70))
                    })
            ).clicked() {
                    self.calculation_mode = CalculationMode::ByFrequency;
                }
                
            ui.add_space(4.0);  // 进一步减少间距
            
            // 仅计算距离模式
            let dist_selected = matches!(self.calculation_mode, CalculationMode::DistanceOnly);
            let dist_color = if dist_selected {
                Color32::from_rgb(60, 180, 120)
                    } else {
                Color32::from_rgb(35, 45, 40)
            };
            
            if ui.add(
                egui::Button::new(RichText::new("📏 仅按距离匹配").size(14.0).strong())
                    .min_size(egui::vec2(button_width, button_height))
                    .fill(dist_color)
                    .stroke(if dist_selected {
                        Stroke::new(2.0, Color32::from_rgb(80, 220, 160))
                    } else {
                        Stroke::new(1.0, Color32::from_rgb(60, 80, 70))
                    })
            ).clicked() {
                    self.calculation_mode = CalculationMode::DistanceOnly;
                }
                
            ui.add_space(4.0);  // 进一步减少间距
            
            // 双向计算模式
            let bi_selected = matches!(self.calculation_mode, CalculationMode::Bidirectional);
            let bi_color = if bi_selected {
                Color32::from_rgb(60, 180, 120)
                    } else {
                Color32::from_rgb(35, 45, 40)
            };
            
            if ui.add(
                egui::Button::new(RichText::new("🔄 双向距离计算").size(14.0).strong())
                    .min_size(egui::vec2(button_width, button_height))
                    .fill(bi_color)
                    .stroke(if bi_selected {
                        Stroke::new(2.0, Color32::from_rgb(80, 220, 160))
                    } else {
                        Stroke::new(1.0, Color32::from_rgb(60, 80, 70))
                    })
            ).clicked() {
                    self.calculation_mode = CalculationMode::Bidirectional;
                }
            
            ui.add_space(3.0);  // 进一步减少间距
            
            // 当前模式说明 - 简化显示
            let description = match self.calculation_mode {
                CalculationMode::ByFrequency => "优先匹配相同频点的站点，提高频点匹配率",
                CalculationMode::DistanceOnly => "忽略频点，仅按物理距离匹配最近站点", 
                CalculationMode::Bidirectional => "计算原始→目标和目标→原始双向匹配结果",
            };
            
            ui.horizontal_wrapped(|ui| {
                ui.label(RichText::new("ℹ️").size(12.0).color(Color32::from_rgb(150, 200, 180)));
            ui.label(
                RichText::new(description)
                        .size(11.0)  // 稍微减小字体
                        .color(Color32::from_rgb(150, 200, 180))
                        .italics()
            );
            });
        });
    }
    
    fn draw_file_selection_compact(&mut self, ui: &mut egui::Ui) {
        ui.vertical(|ui| {
            // 文件选择按钮区域 - 优雅的卡片式布局
            ui.columns(2, |columns| {
                // 输入文件选择卡片
                columns[0].vertical(|ui| {
                    let clicked_input = self.draw_file_selection_card(
                        ui, 
                        "📥 输入文件", 
                        "选择Excel数据文件",
                        &self.input_file_path,
                        Color32::from_rgb(100, 180, 255),
                    );
                    
                    if clicked_input {
                    if let Some(path) = FileDialog::new()
                        .add_filter("Excel文件", &["xlsx", "xls"])
                            .set_title("选择包含站点数据的Excel文件")
                        .pick_file() 
                    {
                        self.input_file_path = Some(path);
                    }
                }
                });
                
                // 输出文件选择卡片
                columns[1].vertical(|ui| {
                    let clicked_output = self.draw_file_selection_card(
                        ui, 
                        "📤 输出文件", 
                        "选择结果保存位置",
                        &self.output_file_path,
                        Color32::from_rgb(100, 255, 180),
                    );
                    
                    if clicked_output {
                    if let Some(path) = FileDialog::new()
                        .add_filter("Excel文件", &["xlsx"])
                            .set_title("选择结果文件保存位置")
                        .save_file() 
                    {
                        self.output_file_path = Some(path);
                    }
                }
            });
            });
        });
    }
    
        fn truncate_path_safely(&self, path_str: &str, max_len: usize) -> String {
        if path_str.chars().count() <= max_len {
            return path_str.to_string();
        }
        
        // 安全地截取字符串，确保在字符边界上
        let chars: Vec<char> = path_str.chars().collect();
        let start_pos = chars.len().saturating_sub(max_len - 3); // 为"..."预留3个字符
        let truncated: String = chars[start_pos..].iter().collect();
        format!("...{}", truncated)
    }
    
    fn draw_file_selection_card(
        &self,
        ui: &mut egui::Ui,
        title: &str,
        description: &str,
        file_path: &Option<PathBuf>,
        accent_color: Color32,
    ) -> bool {
        let is_selected = file_path.is_some();
        let mut clicked = false;
        
        // 卡片背景色根据选择状态变化
        let card_bg = if is_selected {
            Color32::from_rgb(25, 40, 30)
        } else {
            Color32::from_rgb(30, 35, 32)
        };
        
        let border_color = if is_selected {
            accent_color
        } else {
            Color32::from_rgb(60, 80, 70)
        };
        
        egui::Frame::none()
            .fill(card_bg)
            .stroke(Stroke::new(1.5, border_color))
            .rounding(egui::Rounding::same(8.0))
            .inner_margin(egui::Margin::symmetric(12.0, 10.0))
            .show(ui, |ui| {
                ui.vertical(|ui| {
                    // 卡片标题
                    ui.horizontal(|ui| {
                        ui.label(
                            RichText::new(title)
                                .size(12.0)
                                .color(accent_color)
                                .strong()
                        );
                    });
                    
                    ui.add_space(6.0);
                    
                    // 文件状态显示
                    if let Some(path) = file_path {
                        // 已选择文件的展示
                        ui.vertical(|ui| {
                            // 文件信息区域
                            egui::Frame::none()
                                .fill(Color32::from_rgb(20, 30, 25))
                                .stroke(Stroke::new(1.0, Color32::from_rgb(40, 80, 60)))
                                .rounding(egui::Rounding::same(4.0))
                                .inner_margin(egui::Margin::symmetric(8.0, 6.0))
                                .show(ui, |ui| {
                                    ui.horizontal(|ui| {
                                        ui.label(RichText::new("📄").size(14.0));
            ui.add_space(4.0);
                                        ui.vertical(|ui| {
                                            // 文件名
                                            let filename = path.file_name()
                                                .unwrap_or_default()
                                                .to_string_lossy()
                                                .to_string();
                                            ui.label(
                                                RichText::new(filename)
                                                    .size(10.0)
                                                    .color(Color32::from_rgb(200, 255, 220))
                                                    .strong()
                                            );
                                            
                                            // 文件路径
                                            if let Some(parent) = path.parent() {
                                                let parent_str = parent.to_string_lossy();
                                                let display_path = self.truncate_path_safely(&parent_str, 25);
                ui.label(
                                                    RichText::new(display_path)
                                                        .size(8.0)
                                                        .color(Color32::from_rgb(140, 180, 160))
                                                        .italics()
                                                );
                                            }
                                        });
                                    });
                                });
                            
                            ui.add_space(4.0);
                            
                            // 操作按钮
                            ui.horizontal(|ui| {
                                if ui.add(
                                    egui::Button::new("🔄 重新选择")
                                        .min_size(egui::vec2(80.0, 24.0))
                                        .fill(Color32::from_rgb(60, 120, 90))
                                ).clicked() {
                                    clicked = true;
                                }
                                
                                ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                ui.label(
                                        RichText::new("✅")
                                            .size(16.0)
                        .color(Color32::from_rgb(100, 255, 150))
                                    );
                                });
                            });
                        });
                    } else {
                        // 未选择文件的展示
                        ui.vertical_centered(|ui| {
                            ui.add_space(8.0);
                            
                            // 提示图标
                            ui.label(
                                RichText::new("📁")
                                    .size(24.0)
                                    .color(Color32::from_rgb(120, 120, 120))
                            );
                            
                            ui.add_space(4.0);
                            
                            // 描述文字
                            ui.label(
                                RichText::new(description)
                        .size(9.0)
                                    .color(Color32::from_rgb(150, 150, 150))
                            );
                            
                            ui.add_space(6.0);
                            
                            // 选择按钮
                            if ui.add(
                                egui::Button::new("📂 浏览文件")
                                    .min_size(egui::vec2(100.0, 28.0))
                                    .fill(Color32::from_rgb(70, 110, 90))
                            ).clicked() {
                                clicked = true;
                            }
                            
                            ui.add_space(8.0);
                        });
                    }
                });
            });
        
        clicked
    }
    
    fn draw_results_area(&mut self, ui: &mut egui::Ui) {
        // 结果显示区域 - 更丰富的状态展示
        egui::Frame::none()
            .fill(Color32::from_rgb(22, 30, 26))
            .stroke(Stroke::new(2.0, Color32::from_rgb(70, 180, 120)))
            .rounding(egui::Rounding::same(10.0))
            .inner_margin(egui::Margin::symmetric(16.0, 10.0))  // 减小内边距
            .show(ui, |ui| {
                ui.vertical(|ui| {
                    // 区域标题 - 更紧凑
                ui.horizontal(|ui| {
                        ui.label(RichText::new("📊").size(14.0).color(Color32::from_rgb(255, 180, 100)));
                    ui.label(
                            RichText::new("处理状态与结果")
                                .size(12.0)  // 减小字体
                                .color(Color32::from_rgb(255, 200, 120))
                            .strong()
                    );
                });
                
                    ui.add_space(8.0);  // 减少间距
                
                let status = self.processing_status.lock().unwrap().clone();
                match status {
                    ProcessingStatus::Idle => {
                            ui.vertical_centered(|ui| {
                                ui.add_space(10.0);
                                ui.label(RichText::new("⏳").size(48.0));
                                ui.add_space(8.0);
                            ui.label(
                                    RichText::new("等待开始计算")
                                        .size(18.0)
                                        .color(Color32::from_rgb(200, 220, 210))
                                        .strong()
                                );
                                ui.add_space(6.0);
                            ui.label(
                                    RichText::new("请完成文件选择并点击开始计算按钮")
                                        .size(13.0)
                                        .color(Color32::from_rgb(150, 170, 160))
                                );
                                ui.add_space(10.0);
                        });
                    }
                    ProcessingStatus::Completed(_) => {
                        ui.vertical(|ui| {
                            ui.horizontal(|ui| {
                                    ui.label(RichText::new("✅").size(24.0));
                                    ui.add_space(8.0);
                                    ui.vertical(|ui| {
                                ui.label(
                                    RichText::new("计算完成！")
                                                .size(18.0)
                                                .color(Color32::from_rgb(120, 255, 150))
                                        .strong()
                                );
                                        ui.label(
                                            RichText::new("Excel文件已成功生成")
                                                .size(13.0)
                                                .color(Color32::from_rgb(150, 200, 170))
                                        );
                                    });
                                    
                                    ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                                if ui.add(
                                    egui::Button::new("📊 查看详细结果")
                                                .min_size(egui::vec2(120.0, 32.0))
                                                .fill(Color32::from_rgb(60, 140, 100))
                                ).clicked() {
                                    self.show_results_modal = true;
                                }
                                    });
                            });
                            
                            ui.add_space(8.0);
                                
                                // 快速操作按钮
                                ui.horizontal(|ui| {
                                    if ui.add(
                                        egui::Button::new("📁 打开文件夹")
                                            .fill(Color32::from_rgb(80, 120, 140))
                                    ).clicked() {
                                        if let Some(output_path) = &self.output_file_path {
                                            if let Some(parent) = output_path.parent() {
                                                let _ = std::process::Command::new("explorer")
                                                    .arg(parent)
                                                    .spawn();
                                            }
                                        }
                                    }
                                    
                                    if ui.add(
                                        egui::Button::new("🔄 重新计算")
                                            .fill(Color32::from_rgb(100, 140, 80))
                                    ).clicked() {
                                        *self.processing_status.lock().unwrap() = ProcessingStatus::Idle;
                                        self.results_stats = None;
                                    }
                                });
                        });
                    }
                    ProcessingStatus::Error(_) => {
                        ui.horizontal(|ui| {
                                ui.label(RichText::new("❌").size(24.0));
                                ui.add_space(8.0);
                                ui.vertical(|ui| {
                            ui.label(
                                RichText::new("处理失败")
                                            .size(18.0)
                                            .color(Color32::from_rgb(255, 120, 120))
                                    .strong()
                            );
                                    ui.label(
                                        RichText::new("点击查看错误详情")
                                            .size(13.0)
                                            .color(Color32::from_rgb(200, 150, 150))
                                    );
                                });
                                
                                ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                            if ui.add(
                                        egui::Button::new("🔍 查看错误")
                                            .min_size(egui::vec2(100.0, 32.0))
                                    .fill(Color32::from_rgb(180, 60, 60))
                            ).clicked() {
                                if let ProcessingStatus::Error(error_msg) = status {
                                    self.modal_state = ModalState::Error(error_msg);
                                }
                            }
                                });
                        });
                    }
                    _ => {
                            ui.vertical_centered(|ui| {
                                ui.add_space(10.0);
                                ui.spinner();
                                ui.add_space(8.0);
                        ui.label(
                            RichText::new("🔄 正在处理中...")
                                .size(16.0)
                                        .color(Color32::from_rgb(120, 200, 255))
                                        .strong()
                        );
                                ui.add_space(10.0);
                            });
                    }
                }
                });
            });
    }
    
    fn draw_processing_controls(&mut self, ui: &mut egui::Ui, ctx: &egui::Context) {
        let can_process = self.input_file_path.is_some() && 
                          self.output_file_path.is_some() &&
                          matches!(*self.processing_status.lock().unwrap(), ProcessingStatus::Idle | ProcessingStatus::Completed(_) | ProcessingStatus::Error(_));
        
        ui.vertical_centered(|ui| {
            ui.add_space(6.0);  // 减少间距
            
            // 文件准备状态展示 - 使用现有方法
            self.draw_preparation_status_card(ui);
            
            ui.add_space(8.0);  // 减少间距
            
            // 主要操作按钮 - 更紧凑的设计
            let (button_text, button_color, button_icon) = if can_process { 
                ("开始智能计算", Color32::from_rgb(40, 150, 80), "🚀")
            } else { 
                ("请完成文件选择", Color32::from_rgb(120, 70, 50), "⚠️")
            };
            
            // 紧凑按钮外框
            egui::Frame::none()
                .fill(if can_process { 
                    Color32::from_rgb(20, 40, 30) 
                } else { 
                    Color32::from_rgb(40, 30, 25) 
                })
                .stroke(Stroke::new(1.5, if can_process {
                    Color32::from_rgb(60, 180, 120)
                } else {
                    Color32::from_rgb(140, 80, 60)
                }))
                .rounding(egui::Rounding::same(8.0))  // 减小圆角
                .inner_margin(egui::Margin::symmetric(16.0, 6.0))  // 减小内边距
                .show(ui, |ui| {
                    ui.horizontal_centered(|ui| {
                        ui.label(RichText::new(button_icon).size(16.0));  // 减小图标
                        ui.add_space(6.0);
            
            if ui.add_enabled(
                can_process, 
                            egui::Button::new(RichText::new(button_text).size(14.0).strong())  // 减小字体
                                .min_size(egui::vec2(150.0, 32.0))  // 减小按钮尺寸
                    .fill(button_color)
                                .stroke(Stroke::new(1.0, if can_process {
                                    Color32::from_rgb(80, 200, 140)
                                } else {
                                    Color32::from_rgb(160, 100, 80)
                                }))
            ).clicked() {
                self.start_processing(ctx);
                        }
                    });
                });
        });
    }
    
    fn draw_preparation_status_card(&self, ui: &mut egui::Ui) {
        // 准备状态卡片 - 紧凑版
        egui::Frame::none()
            .fill(Color32::from_rgb(25, 33, 28))
            .stroke(Stroke::new(1.0, Color32::from_rgb(70, 140, 100)))
            .rounding(egui::Rounding::same(6.0))  // 减小圆角
            .inner_margin(egui::Margin::symmetric(12.0, 8.0))  // 减小内边距
            .show(ui, |ui| {
                ui.vertical(|ui| {
                    // 状态标题 - 紧凑
                    ui.horizontal(|ui| {
                        ui.label(RichText::new("📋").size(12.0).color(Color32::from_rgb(120, 200, 160)));
                        ui.add_space(4.0);
                        ui.label(
                            RichText::new("准备状态")  // 简化文字
                                .size(11.0)  // 减小字体
                                .color(Color32::from_rgb(150, 220, 180))
                                .strong()
                        );
                    });
                    
                    ui.add_space(6.0);  // 减少间距
                    
                    // 状态指示器 - 更紧凑的设计
                    ui.columns(2, |columns| {
                        // 输入文件状态
                        columns[0].horizontal(|ui| {
                            let (icon, text, color) = if self.input_file_path.is_some() {
                                ("✅", "输入已选择", Color32::from_rgb(100, 255, 150))
                            } else {
                                ("⭕", "待选择输入", Color32::from_rgb(255, 180, 120))
                            };
                            
                            ui.label(RichText::new(icon).size(12.0));  // 减小图标
                            ui.add_space(3.0);
                            ui.label(RichText::new(text).size(9.0).color(color));  // 减小字体
                        });
                        
                        // 输出文件状态
                        columns[1].horizontal(|ui| {
                            let (icon, text, color) = if self.output_file_path.is_some() {
                                ("✅", "输出已设置", Color32::from_rgb(100, 255, 150))
                            } else {
                                ("⭕", "待设置输出", Color32::from_rgb(255, 180, 120))
                            };
                            
                            ui.label(RichText::new(icon).size(12.0));  // 减小图标
                            ui.add_space(3.0);
                            ui.label(RichText::new(text).size(9.0).color(color));  // 减小字体
                        });
                    });
                    
                    // 整体准备度 - 简化
                    if self.input_file_path.is_some() && self.output_file_path.is_some() {
                        ui.add_space(4.0);  // 减少间距
                        ui.horizontal_centered(|ui| {
                            ui.label(RichText::new("🎯").size(10.0));
                            ui.add_space(3.0);
                            ui.label(
                                RichText::new("就绪，可开始计算")  // 简化文字
                                    .size(8.0)  // 减小字体
                                    .color(Color32::from_rgb(150, 255, 180))
                                    .italics()
                            );
                        });
                    }
            });
        });
    }
    
    fn show_modals(&mut self, ctx: &egui::Context) {
        let status = self.processing_status.lock().unwrap().clone();
        
        // 显示计算进度弹窗 - 更优雅的设计
        if matches!(status, ProcessingStatus::Loading | ProcessingStatus::Processing(_)) {
            egui::Window::new("🔄 计算进度")
                .collapsible(false)
                .resizable(false)
                .default_width(450.0)
                .default_height(220.0)
                .anchor(egui::Align2::CENTER_CENTER, egui::vec2(0.0, 0.0))
                .frame(egui::Frame::window(&ctx.style()).fill(Color32::from_rgb(25, 35, 30)))
                .show(ctx, |ui| {
                    ui.vertical_centered(|ui| {
                        match status {
                            ProcessingStatus::Loading => {
                                ui.add_space(25.0);
                                
                                // 加载动画区域
                                ui.horizontal(|ui| {
                                ui.add_space(10.0);
                                    ui.spinner();
                                    ui.add_space(15.0);
                                ui.label(
                                    RichText::new("📖 正在加载Excel文件...")
                                            .size(18.0)
                                            .color(Color32::from_rgb(120, 200, 255))
                                            .strong()
                                    );
                                });
                                
                                ui.add_space(15.0);
                                
                                // 提示信息
                                egui::Frame::none()
                                    .fill(Color32::from_rgb(30, 40, 35))
                                    .stroke(Stroke::new(1.0, Color32::from_rgb(80, 120, 100)))
                                    .rounding(egui::Rounding::same(8.0))
                                    .inner_margin(egui::Margin::symmetric(15.0, 10.0))
                                    .show(ui, |ui| {
                                ui.label(
                                            RichText::new("正在读取站点数据，请稍候...")
                                                .size(13.0)
                                                .color(Color32::from_rgb(170, 200, 185))
                                        );
                                    });
                                
                                ui.add_space(25.0);
                            }
                            ProcessingStatus::Processing(progress) => {
                                ui.add_space(25.0);
                                
                                // 进度标题
                                ui.label(
                                    RichText::new("🚀 正在计算距离匹配...")
                                        .size(20.0)
                                        .color(Color32::from_rgb(80, 220, 160))
                                        .strong()
                                );
                                
                                ui.add_space(20.0);
                                
                                // 进度条区域
                                egui::Frame::none()
                                    .fill(Color32::from_rgb(30, 40, 35))
                                    .stroke(Stroke::new(1.5, Color32::from_rgb(80, 180, 130)))
                                    .rounding(egui::Rounding::same(10.0))
                                    .inner_margin(egui::Margin::symmetric(20.0, 15.0))
                                    .show(ui, |ui| {
                                        ui.vertical_centered(|ui| {
                                            // 进度百分比
                                            ui.label(
                                                RichText::new(format!("{:.1}%", progress * 100.0))
                                                    .size(24.0)
                                                    .color(Color32::from_rgb(100, 255, 180))
                                                    .strong()
                                                    .monospace()
                                            );
                                            
                                            ui.add_space(8.0);
                                
                                // 进度条
                                ui.add(
                                    egui::ProgressBar::new(progress)
                                                    .fill(Color32::from_rgb(60, 200, 120))
                                                    .desired_width(320.0)
                                                    .desired_height(12.0)
                                                    .rounding(egui::Rounding::same(6.0))
                                            );
                                            
                                            ui.add_space(5.0);
                                            
                                            // 状态文字
                                ui.label(
                                                RichText::new("正在计算站点间距离，请稍候...")
                                        .size(12.0)
                                                    .color(Color32::from_rgb(150, 200, 175))
                                );
                                        });
                                    });
                                
                                ui.add_space(25.0);
                            }
                            _ => {}
                        }
                    });
                });
        }
        
        // 显示结果弹窗 - 更丰富的信息展示
        if self.show_results_modal {
            egui::Window::new("📊 计算结果详情")
                .collapsible(false)
                .resizable(true)
                .default_width(750.0)
                .default_height(550.0)
                .anchor(egui::Align2::CENTER_CENTER, egui::vec2(0.0, 0.0))
                .frame(egui::Frame::window(&ctx.style()).fill(Color32::from_rgb(22, 30, 26)))
                .show(ctx, |ui| {
                    ui.vertical_centered(|ui| {
                        // 标题区域 - 调整圆角大小与其他方框一致
                        egui::Frame::none()
                            .fill(Color32::from_rgb(28, 38, 32))
                            .stroke(Stroke::new(2.0, Color32::from_rgb(70, 180, 120)))
                            .rounding(egui::Rounding::same(10.0))  // 与主界面方框保持一致
                            .inner_margin(egui::Margin::symmetric(24.0, 16.0))  // 与主界面方框保持一致
                            .show(ui, |ui| {
                                ui.horizontal(|ui| {
                                    ui.label(RichText::new("📈").size(20.0));
                                    ui.add_space(8.0);
                                    ui.label(
                                        RichText::new("详细统计结果")
                                            .size(18.0)
                                            .color(Color32::from_rgb(80, 220, 160))
                                            .strong()
                                    );
                                });
                            });
                        
                        ui.add_space(15.0);
                        
                        // 统计内容区域 - 居中显示
                        if let Some(stats) = &self.results_stats {
                            egui::Frame::none()
                                .fill(Color32::from_rgb(25, 33, 28))
                                .stroke(Stroke::new(1.5, Color32::from_rgb(60, 140, 100)))
                                .rounding(egui::Rounding::same(8.0))
                                .inner_margin(egui::Margin::symmetric(20.0, 16.0))
                                .show(ui, |ui| {
                                    ui.vertical_centered(|ui| {
                                        egui::ScrollArea::vertical()
                                            .max_height(350.0)
                                            .show(ui, |ui| {
                                                ui.vertical_centered(|ui| {
                                                    ui.label(
                                                        RichText::new(stats)
                                                            .size(14.0)
                                                            .color(Color32::from_rgb(220, 255, 235))
                                                            .monospace()
                                                    );
                                                });
                                            });
                                    });
                                });
                        } else {
                            ui.vertical_centered(|ui| {
                                ui.add_space(50.0);
                                ui.label(RichText::new("📋").size(48.0));
                                ui.add_space(10.0);
                                ui.label(
                                    RichText::new("暂无结果数据")
                                        .size(16.0)
                                        .color(Color32::from_rgb(180, 180, 180))
                                );
                                ui.add_space(50.0);
                            });
                        }
                    
                        ui.add_space(15.0);
                        
                        // 底部操作按钮 - 与其他方框样式保持一致
                        egui::Frame::none()
                            .fill(Color32::from_rgb(28, 38, 32))
                            .stroke(Stroke::new(2.0, Color32::from_rgb(70, 180, 120)))
                            .rounding(egui::Rounding::same(10.0))  // 与主界面方框保持一致
                            .inner_margin(egui::Margin::symmetric(24.0, 12.0))  // 与主界面方框保持一致
                            .show(ui, |ui| {
                                ui.horizontal(|ui| {
                                    if ui.add(
                                        egui::Button::new("🗂️ 打开文件夹")
                                            .min_size(egui::vec2(120.0, 32.0))
                                            .fill(Color32::from_rgb(70, 130, 110))
                                    ).clicked() {
                                        if let Some(output_path) = &self.output_file_path {
                                            if let Some(parent) = output_path.parent() {
                                                let _ = std::process::Command::new("explorer")
                                                    .arg(parent)
                                                    .spawn();
                                            }
                                        }
                                    }
                                    
                                    ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                                        if ui.add(
                                            egui::Button::new("✅ 关闭")
                                                .min_size(egui::vec2(80.0, 32.0))
                                                .fill(Color32::from_rgb(60, 150, 110))
                                        ).clicked() {
                                            self.show_results_modal = false;
                                        }
                                    });
                                });
                            });
                    });
                });
        }
        
        // 显示错误弹窗 - 更友好的错误展示
        if let ModalState::Error(error_msg) = &self.modal_state.clone() {
            egui::Window::new("❌ 错误详情")
                .collapsible(false)
                .resizable(true)
                .default_width(650.0)
                .default_height(400.0)
                .anchor(egui::Align2::CENTER_CENTER, egui::vec2(0.0, 0.0))
                .frame(egui::Frame::window(&ctx.style()).fill(Color32::from_rgb(30, 25, 25)))
                .show(ctx, |ui| {
                    ui.vertical(|ui| {
                        // 错误标题区域
                        egui::Frame::none()
                            .fill(Color32::from_rgb(40, 25, 25))
                            .stroke(Stroke::new(1.5, Color32::from_rgb(255, 120, 120)))
                            .rounding(egui::Rounding::same(8.0))
                            .inner_margin(egui::Margin::symmetric(15.0, 10.0))
                            .show(ui, |ui| {
                    ui.horizontal(|ui| {
                                    ui.label(RichText::new("⚠️").size(20.0));
                                    ui.add_space(8.0);
                        ui.label(
                            RichText::new("处理过程中发生错误")
                                            .size(18.0)
                                            .color(Color32::from_rgb(255, 140, 140))
                                .strong()
                        );
                                });
                    });
                    
                        ui.add_space(15.0);
                    
                        // 错误信息标签
                    ui.label(
                        RichText::new("错误详情：")
                                .size(15.0)
                            .color(Color32::from_rgb(255, 200, 200))
                            .strong()
                    );
                    
                    ui.add_space(10.0);
                    
                        // 错误内容区域
                        egui::Frame::none()
                            .fill(Color32::from_rgb(35, 28, 28))
                            .stroke(Stroke::new(1.0, Color32::from_rgb(180, 80, 80)))
                            .rounding(egui::Rounding::same(6.0))
                            .inner_margin(egui::Margin::symmetric(15.0, 12.0))
                            .show(ui, |ui| {
                    egui::ScrollArea::vertical()
                                    .max_height(220.0)
                        .show(ui, |ui| {
                            ui.label(
                                RichText::new(error_msg)
                                                .size(13.0)
                                                .color(Color32::from_rgb(255, 200, 200))
                                    .monospace()
                            );
                                    });
                        });
                    
                    ui.add_space(15.0);
                        
                        // 底部按钮
                        egui::Frame::none()
                            .fill(Color32::from_rgb(35, 28, 28))
                            .stroke(Stroke::new(1.0, Color32::from_rgb(140, 60, 60)))
                            .rounding(egui::Rounding::same(6.0))
                            .inner_margin(egui::Margin::symmetric(15.0, 8.0))
                            .show(ui, |ui| {
                    ui.horizontal(|ui| {
                        ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                            if ui.add(
                                egui::Button::new("✅ 确定")
                                                .min_size(egui::vec2(80.0, 32.0))
                                                .fill(Color32::from_rgb(180, 70, 70))
                            ).clicked() {
                                self.modal_state = ModalState::None;
                            }
                                    });
                                });
                        });
                    });
                });
        }
    }
    
    fn start_processing(&mut self, ctx: &egui::Context) {
        let input_path = self.input_file_path.clone().unwrap();
        let output_path = self.output_file_path.clone().unwrap();
        let mode = self.calculation_mode.clone();
        let status = Arc::clone(&self.processing_status);
        let ctx = ctx.clone();
        
        // 重置状态
        *status.lock().unwrap() = ProcessingStatus::Loading;
        
        let ctx_clone = ctx.clone();
        
        thread::spawn(move || {
            let result = process_data(&input_path, &output_path, mode, Arc::clone(&status), &ctx_clone);
            
            match result {
                Ok(stats) => {
                    *status.lock().unwrap() = ProcessingStatus::Completed(stats);
                }
                Err(e) => {
                    *status.lock().unwrap() = ProcessingStatus::Error(e.to_string());
                }
            }
            
            ctx_clone.request_repaint();
        });
    }
}

fn process_data(
    input_path: &PathBuf,
    output_path: &PathBuf,
    mode: CalculationMode,
    status: Arc<Mutex<ProcessingStatus>>,
    ctx: &egui::Context,
) -> Result<String> {
    // 读取数据
    let source_data = read_excel_data(input_path.to_str().unwrap(), "原始数据")?;
    let target_data = read_excel_data(input_path.to_str().unwrap(), "目标数据")?;
    
    ctx.request_repaint();
    
    // 根据模式进行计算
    match mode {
        CalculationMode::ByFrequency => {
            let results = find_nearest_ecgi_by_frequency(&source_data, &target_data, Arc::clone(&status), ctx)?;
            create_beautified_excel(&results, output_path.to_str().unwrap())?;
            Ok(generate_statistics_string(&results))
        }
        CalculationMode::DistanceOnly => {
            let results = find_nearest_ecgi(&source_data, &target_data, Arc::clone(&status), ctx)?;
            create_beautified_excel(&results, output_path.to_str().unwrap())?;
            Ok(generate_statistics_string(&results))
        }
        CalculationMode::Bidirectional => {
            let forward_results = find_nearest_ecgi(&source_data, &target_data, Arc::clone(&status), ctx)?;
            let backward_results = find_nearest_ecgi(&target_data, &source_data, Arc::clone(&status), ctx)?;
            create_bidirectional_excel(&forward_results, &backward_results, output_path.to_str().unwrap())?;
            Ok(generate_bidirectional_statistics_string(&forward_results, &backward_results))
        }
    }
}

/// 从Excel读取数据
fn read_excel_data(filename: &str, sheet_name: &str) -> Result<Vec<SiteData>> {
    let mut workbook: Xlsx<_> = open_workbook(filename)
        .context(format!("无法打开文件: {}", filename))?;
    
    let range = workbook
        .worksheet_range(sheet_name)
        .context(format!("无法读取sheet: {}", sheet_name))?;
    
    let mut data = Vec::new();
    let mut ecgi_set = HashSet::new();
    
    // 获取表头并建立列索引
    let headers: Vec<String> = range
        .rows()
        .next()
        .context("无法获取表头")?
        .iter()
        .map(|cell| cell.to_string())
        .collect();
    
    let mut col_indices = HashMap::new();
    for (i, header) in headers.iter().enumerate() {
        col_indices.insert(header.clone(), i);
    }
    
    let required_columns = vec!["ECGI", "站号", "经度", "纬度", "频点"];
    for col in &required_columns {
        if !col_indices.contains_key(*col) {
            return Err(ProcessingError::MissingColumns {
                columns: vec![col.to_string()],
            }.into());
        }
    }
    
    // 读取数据行
    for (row_idx, row) in range.rows().skip(1).enumerate() {
        if row.is_empty() {
            continue;
        }
        
        let ecgi_idx = col_indices["ECGI"];
        let site_idx = col_indices["站号"];
        let lon_idx = col_indices["经度"];
        let lat_idx = col_indices["纬度"];
        let freq_idx = col_indices["频点"];
        
        // 安全地获取单元格值
        let ecgi = if ecgi_idx < row.len() {
            row[ecgi_idx].to_string()
        } else {
            return Err(ProcessingError::DataFormatError {
                message: format!("第{}行ECGI列为空", row_idx + 2),
            }.into());
        };
        
        // 跳过空的ECGI
        if ecgi.trim().is_empty() {
            continue;
        }
        
        // 检查ECGI重复
        if !ecgi_set.insert(ecgi.clone()) {
            continue; // 跳过重复项
        }
        
        let site_number = if site_idx < row.len() {
            row[site_idx].to_string()
        } else {
            String::new()
        };
        
        let longitude: f64 = if lon_idx < row.len() {
            match &row[lon_idx] {
                Data::Float(f) => *f,
                Data::Int(i) => *i as f64,
                Data::String(s) => s.parse()
                    .context(format!("第{}行经度格式错误: {}", row_idx + 2, s))?,
                _ => return Err(ProcessingError::DataFormatError {
                    message: format!("第{}行经度格式错误", row_idx + 2),
                }.into()),
            }
        } else {
            return Err(ProcessingError::DataFormatError {
                message: format!("第{}行经度列为空", row_idx + 2),
            }.into());
        };
        
        let latitude: f64 = if lat_idx < row.len() {
            match &row[lat_idx] {
                Data::Float(f) => *f,
                Data::Int(i) => *i as f64,
                Data::String(s) => s.parse()
                    .context(format!("第{}行纬度格式错误: {}", row_idx + 2, s))?,
                _ => return Err(ProcessingError::DataFormatError {
                    message: format!("第{}行纬度格式错误", row_idx + 2),
                }.into()),
            }
        } else {
            return Err(ProcessingError::DataFormatError {
                message: format!("第{}行纬度列为空", row_idx + 2),
            }.into());
        };
        
        let frequency: i32 = if freq_idx < row.len() {
            match &row[freq_idx] {
                Data::Float(f) => *f as i32,
                Data::Int(i) => *i as i32,
                Data::String(s) => s.parse()
                    .context(format!("第{}行频点格式错误: {}", row_idx + 2, s))?,
                _ => return Err(ProcessingError::DataFormatError {
                    message: format!("第{}行频点格式错误", row_idx + 2),
                }.into()),
            }
        } else {
            return Err(ProcessingError::DataFormatError {
                message: format!("第{}行频点列为空", row_idx + 2),
            }.into());
        };
        
        data.push(SiteData {
            ecgi,
            site_number,
            longitude,
            latitude,
            frequency,
        });
    }
    
    Ok(data)
}

/// 使用Haversine公式计算两点间距离（单位：公里）
fn haversine_distance(lat1: f64, lon1: f64, lat2: f64, lon2: f64) -> f64 {
    let r = 6371.0; // 地球半径（公里）
    
    let lat1_rad = lat1 * PI / 180.0;
    let lat2_rad = lat2 * PI / 180.0;
    let delta_lat = (lat2 - lat1) * PI / 180.0;
    let delta_lon = (lon2 - lon1) * PI / 180.0;
    
    let a = (delta_lat / 2.0).sin() * (delta_lat / 2.0).sin()
        + lat1_rad.cos() * lat2_rad.cos() * (delta_lon / 2.0).sin() * (delta_lon / 2.0).sin();
    let c = 2.0 * a.sqrt().asin();
    
    r * c
}

/// 找到最近的ECGI匹配（按距离）
fn find_nearest_ecgi(
    source_data: &[SiteData], 
    target_data: &[SiteData],
    status: Arc<Mutex<ProcessingStatus>>,
    ctx: &egui::Context,
) -> Result<Vec<MatchResult>> {
    let mut results = Vec::new();
    let total = source_data.len();
    
    for (idx, source) in source_data.iter().enumerate() {
        let mut min_distance = f64::INFINITY;
        let mut nearest_target: Option<&SiteData> = None;
        
        // 计算到所有目标点的距离
        for target in target_data {
            let distance = haversine_distance(
                source.latitude,
                source.longitude,
                target.latitude,
                target.longitude,
            );
            
            if distance < min_distance {
                min_distance = distance;
                nearest_target = Some(target);
            }
        }
        
        if let Some(target) = nearest_target {
            let frequency_match = source.frequency == target.frequency;
            
            results.push(MatchResult {
                source_ecgi: source.ecgi.clone(),
                source_site_number: source.site_number.clone(),
                source_longitude: source.longitude,
                source_latitude: source.latitude,
                source_frequency: source.frequency,
                target_ecgi: target.ecgi.clone(),
                target_site_number: target.site_number.clone(),
                target_longitude: target.longitude,
                target_latitude: target.latitude,
                target_frequency: target.frequency,
                distance_km: (min_distance * 1000.0).round() / 1000.0,
                frequency_match,
            });
        }
        
        // 更新进度
        let progress = (idx + 1) as f32 / total as f32;
        *status.lock().unwrap() = ProcessingStatus::Processing(progress);
        ctx.request_repaint();
    }
    
    Ok(results)
}

/// 找到最近的ECGI匹配（按频点优先）
fn find_nearest_ecgi_by_frequency(
    source_data: &[SiteData], 
    target_data: &[SiteData],
    status: Arc<Mutex<ProcessingStatus>>,
    ctx: &egui::Context,
) -> Result<Vec<MatchResult>> {
    let mut results = Vec::new();
    let total = source_data.len();
    
    for (idx, source) in source_data.iter().enumerate() {
        let mut min_distance = f64::INFINITY;
        let mut nearest_target: Option<&SiteData> = None;
        let mut frequency_matched = false;
        
        // 首先寻找相同频点的站点
        for target in target_data {
            if target.frequency == source.frequency {
                let distance = haversine_distance(
                    source.latitude,
                    source.longitude,
                    target.latitude,
                    target.longitude,
                );
                
                if distance < min_distance {
                    min_distance = distance;
                    nearest_target = Some(target);
                    frequency_matched = true;
                }
            }
        }
        
        // 如果没有找到相同频点的，则找最近的任意频点站点
        if !frequency_matched {
            for target in target_data {
                let distance = haversine_distance(
                    source.latitude,
                    source.longitude,
                    target.latitude,
                    target.longitude,
                );
                
                if distance < min_distance {
                    min_distance = distance;
                    nearest_target = Some(target);
                }
            }
        }
        
        if let Some(target) = nearest_target {
            results.push(MatchResult {
                source_ecgi: source.ecgi.clone(),
                source_site_number: source.site_number.clone(),
                source_longitude: source.longitude,
                source_latitude: source.latitude,
                source_frequency: source.frequency,
                target_ecgi: target.ecgi.clone(),
                target_site_number: target.site_number.clone(),
                target_longitude: target.longitude,
                target_latitude: target.latitude,
                target_frequency: target.frequency,
                distance_km: (min_distance * 1000.0).round() / 1000.0,
                frequency_match: frequency_matched,
            });
        }
        
        // 更新进度
        let progress = (idx + 1) as f32 / total as f32;
        *status.lock().unwrap() = ProcessingStatus::Processing(progress);
        ctx.request_repaint();
    }
    
    Ok(results)
}

/// 生成统计报告字符串
fn generate_statistics_string(results: &[MatchResult]) -> String {
    let total_count = results.len();
    let frequency_matched = results.iter().filter(|r| r.frequency_match).count();
    let frequency_unmatched = total_count - frequency_matched;
    
    // 距离统计
    let distances: Vec<f64> = results.iter().map(|r| r.distance_km).collect();
    let min_distance = distances.iter().fold(f64::INFINITY, |a, &b| a.min(b));
    let max_distance = distances.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
    let avg_distance = distances.iter().sum::<f64>() / distances.len() as f64;
    
    format!(
        "📊 计算结果统计\n\n🎯 匹配总数: {} 个站点\n\n📍 频点匹配情况:\n  ✅ 相同频点: {} 个 ({:.1}%)\n  ❌ 不同频点: {} 个 ({:.1}%)\n\n📏 距离统计:\n  📈 平均距离: {:.3} 公里\n  📉 最小距离: {:.3} 公里\n  📊 最大距离: {:.3} 公里",
        total_count,
        frequency_matched,
        frequency_matched as f64 / total_count as f64 * 100.0,
        frequency_unmatched,
        frequency_unmatched as f64 / total_count as f64 * 100.0,
        avg_distance,
        min_distance,
        max_distance
    )
}

/// 生成双向统计报告字符串
fn generate_bidirectional_statistics_string(forward_results: &[MatchResult], backward_results: &[MatchResult]) -> String {
    let forward_stats = generate_statistics_string(forward_results);
    let backward_stats = generate_statistics_string(backward_results);
    
    format!(
        "🔄 双向计算结果对比\n\n📤 【原始 → 目标】\n{}\n\n📥 【目标 → 原始】\n{}",
        forward_stats,
        backward_stats
    )
}

/// 创建美化的Excel文件
fn create_beautified_excel(results: &[MatchResult], output_filename: &str) -> Result<()> {
    let mut workbook = Workbook::new();
    let worksheet = workbook.add_worksheet();
    worksheet.set_name("最近同频小区匹配结果")?;
    
    // 定义格式
    let header_format = Format::new()
        .set_bold()
        .set_font_color(Color::White)
        .set_background_color(Color::RGB(0x366092))
        .set_align(FormatAlign::Center)
        .set_border(FormatBorder::Thin);
    
    let data_format = Format::new()
        .set_align(FormatAlign::Center)
        .set_border(FormatBorder::Thin);
    
    let frequency_mismatch_format = Format::new()
        .set_align(FormatAlign::Center)
        .set_border(FormatBorder::Thin)
        .set_background_color(Color::RGB(0xFFF2CC));
    
    // 写入表头
    let headers = vec![
        "Source ECGI", "Source Site", "Source Longitude", "Source Latitude", "Source Frequency",
        "Target ECGI", "Target Site", "Target Longitude", "Target Latitude", "Target Frequency",
        "Distance (km)", "Frequency Match"
    ];
    
    for (col, &header) in headers.iter().enumerate() {
        worksheet.write_string_with_format(0, col as u16, header, &header_format)?;
    }
    
    // 写入数据
    for (row, result) in results.iter().enumerate() {
        let row_idx = (row + 1) as u32;
        
        worksheet.write_string_with_format(row_idx, 0, &result.source_ecgi, &data_format)?;
        worksheet.write_string_with_format(row_idx, 1, &result.source_site_number, &data_format)?;
        worksheet.write_number_with_format(row_idx, 2, result.source_longitude, &data_format)?;
        worksheet.write_number_with_format(row_idx, 3, result.source_latitude, &data_format)?;
        worksheet.write_number_with_format(row_idx, 4, result.source_frequency as f64, &data_format)?;
        
        worksheet.write_string_with_format(row_idx, 5, &result.target_ecgi, &data_format)?;
        worksheet.write_string_with_format(row_idx, 6, &result.target_site_number, &data_format)?;
        worksheet.write_number_with_format(row_idx, 7, result.target_longitude, &data_format)?;
        worksheet.write_number_with_format(row_idx, 8, result.target_latitude, &data_format)?;
        worksheet.write_number_with_format(row_idx, 9, result.target_frequency as f64, &data_format)?;
        
        worksheet.write_number_with_format(row_idx, 10, result.distance_km, &data_format)?;
        
        let freq_match_text = if result.frequency_match { "是" } else { "否" };
        let freq_format = if result.frequency_match { &data_format } else { &frequency_mismatch_format };
        worksheet.write_string_with_format(row_idx, 11, freq_match_text, freq_format)?;
    }
    
    // 设置列宽
    let column_widths = vec![15.0, 12.0, 12.0, 12.0, 10.0, 15.0, 12.0, 12.0, 12.0, 10.0, 12.0, 10.0];
    for (col, &width) in column_widths.iter().enumerate() {
        worksheet.set_column_width(col as u16, width)?;
    }
    
    // 冻结首行
    worksheet.set_freeze_panes(1, 0)?;
    
    workbook.save(output_filename).context("无法保存Excel文件")?;
    Ok(())
}

/// 创建双向对比Excel文件
fn create_bidirectional_excel(forward_results: &[MatchResult], backward_results: &[MatchResult], output_filename: &str) -> Result<()> {
    let mut workbook = Workbook::new();
    
    let forward_worksheet = workbook.add_worksheet();
    forward_worksheet.set_name("原始→目标")?;
    write_results_to_worksheet(forward_worksheet, forward_results)?;
    
    let backward_worksheet = workbook.add_worksheet();
    backward_worksheet.set_name("目标→原始")?;
    write_results_to_worksheet(backward_worksheet, backward_results)?;
    
    let comparison_worksheet = workbook.add_worksheet();
    comparison_worksheet.set_name("双向对比分析")?;
    create_comparison_analysis(comparison_worksheet, forward_results, backward_results)?;
    
    workbook.save(output_filename).context("无法保存Excel文件")?;
    Ok(())
}

/// 向worksheet写入结果
fn write_results_to_worksheet(worksheet: &mut rust_xlsxwriter::Worksheet, results: &[MatchResult]) -> Result<()> {
    // 定义格式
    let header_format = Format::new()
        .set_bold()
        .set_font_color(Color::White)
        .set_background_color(Color::RGB(0x366092))
        .set_align(FormatAlign::Center)
        .set_border(FormatBorder::Thin);
    
    let data_format = Format::new()
        .set_align(FormatAlign::Center)
        .set_border(FormatBorder::Thin);
    
    let frequency_mismatch_format = Format::new()
        .set_align(FormatAlign::Center)
        .set_border(FormatBorder::Thin)
        .set_background_color(Color::RGB(0xFFF2CC));
    
    // 写入表头
    let headers = vec![
        "源ECGI", "源站号", "源经度", "源纬度", "源频点",
        "目标ECGI", "目标站号", "目标经度", "目标纬度", "目标频点",
        "距离(公里)", "频点匹配"
    ];
    
    for (col, &header) in headers.iter().enumerate() {
        worksheet.write_string_with_format(0, col as u16, header, &header_format)?;
    }
    
    // 写入数据
    for (row, result) in results.iter().enumerate() {
        let row_idx = (row + 1) as u32;
        
        worksheet.write_string_with_format(row_idx, 0, &result.source_ecgi, &data_format)?;
        worksheet.write_string_with_format(row_idx, 1, &result.source_site_number, &data_format)?;
        worksheet.write_number_with_format(row_idx, 2, result.source_longitude, &data_format)?;
        worksheet.write_number_with_format(row_idx, 3, result.source_latitude, &data_format)?;
        worksheet.write_number_with_format(row_idx, 4, result.source_frequency as f64, &data_format)?;
        
        worksheet.write_string_with_format(row_idx, 5, &result.target_ecgi, &data_format)?;
        worksheet.write_string_with_format(row_idx, 6, &result.target_site_number, &data_format)?;
        worksheet.write_number_with_format(row_idx, 7, result.target_longitude, &data_format)?;
        worksheet.write_number_with_format(row_idx, 8, result.target_latitude, &data_format)?;
        worksheet.write_number_with_format(row_idx, 9, result.target_frequency as f64, &data_format)?;
        
        worksheet.write_number_with_format(row_idx, 10, result.distance_km, &data_format)?;
        
        let freq_match_text = if result.frequency_match { "是" } else { "否" };
        let freq_format = if result.frequency_match { &data_format } else { &frequency_mismatch_format };
        worksheet.write_string_with_format(row_idx, 11, freq_match_text, freq_format)?;
    }
    
    // 设置列宽
    let column_widths = vec![15.0, 12.0, 12.0, 12.0, 10.0, 15.0, 12.0, 12.0, 12.0, 10.0, 12.0, 10.0];
    for (col, &width) in column_widths.iter().enumerate() {
        worksheet.set_column_width(col as u16, width)?;
    }
    
    // 冻结首行
    worksheet.set_freeze_panes(1, 0)?;
    
    Ok(())
}

/// 创建双向对比分析sheet
fn create_comparison_analysis(worksheet: &mut rust_xlsxwriter::Worksheet, forward_results: &[MatchResult], backward_results: &[MatchResult]) -> Result<()> {
    let header_format = Format::new()
        .set_bold()
        .set_font_color(Color::White)
        .set_background_color(Color::RGB(0x366092))
        .set_align(FormatAlign::Center)
        .set_border(FormatBorder::Thin);
    
    let stats_format = Format::new()
        .set_align(FormatAlign::Left)
        .set_border(FormatBorder::Thin);
    
    // 标题
    worksheet.write_string_with_format(0, 0, "双向匹配对比分析", &header_format)?;
    
    let mut row = 2;
    
    // 原始→目标统计
    worksheet.write_string_with_format(row, 0, "原始→目标统计", &header_format)?;
    row += 1;
    
    let forward_total = forward_results.len();
    let forward_freq_match = forward_results.iter().filter(|r| r.frequency_match).count();
    let forward_distances: Vec<f64> = forward_results.iter().map(|r| r.distance_km).collect();
    let forward_avg_distance = forward_distances.iter().sum::<f64>() / forward_distances.len() as f64;
    
    worksheet.write_string_with_format(row, 0, &format!("总匹配数: {}", forward_total), &stats_format)?;
    row += 1;
    worksheet.write_string_with_format(row, 0, &format!("频点匹配数: {} ({:.1}%)", forward_freq_match, forward_freq_match as f64 / forward_total as f64 * 100.0), &stats_format)?;
    row += 1;
    worksheet.write_string_with_format(row, 0, &format!("平均距离: {:.3} 公里", forward_avg_distance), &stats_format)?;
    row += 2;
    
    // 目标→原始统计
    worksheet.write_string_with_format(row, 0, "目标→原始统计", &header_format)?;
    row += 1;
    
    let backward_total = backward_results.len();
    let backward_freq_match = backward_results.iter().filter(|r| r.frequency_match).count();
    let backward_distances: Vec<f64> = backward_results.iter().map(|r| r.distance_km).collect();
    let backward_avg_distance = backward_distances.iter().sum::<f64>() / backward_distances.len() as f64;
    
    worksheet.write_string_with_format(row, 0, &format!("总匹配数: {}", backward_total), &stats_format)?;
    row += 1;
    worksheet.write_string_with_format(row, 0, &format!("频点匹配数: {} ({:.1}%)", backward_freq_match, backward_freq_match as f64 / backward_total as f64 * 100.0), &stats_format)?;
    row += 1;
    worksheet.write_string_with_format(row, 0, &format!("平均距离: {:.3} 公里", backward_avg_distance), &stats_format)?;
    
    worksheet.set_column_width(0, 30.0)?;
    
    Ok(())
}

fn main() -> Result<(), eframe::Error> {
    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([950.0, 720.0])  // 优化默认窗口尺寸以适应新布局
            .with_min_inner_size([750.0, 600.0])  // 降低最小尺寸要求，适应紧凑布局
            .with_resizable(true)
            .with_title("ECGI站点距离计算器 v2.0"),
        ..Default::default()
    };
    
    eframe::run_native(
        "🌐 ECGI站点距离计算器",
        options,
        Box::new(|_cc| Ok(Box::new(EcgiCalculatorApp::default()))),
    )
} 